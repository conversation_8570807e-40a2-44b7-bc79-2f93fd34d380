intent: k8s_resource
flavor: k8s
version: "0.2"
description: Adds kubernetes objects inside the cluster via yaml manifests
clouds:
- aws
- azure
- gcp
- kubernetes
spec:
  title: K8s resource
  description: Specification of the K8s resource intent
  type: object
  properties:
    resource:
      type: object
      title: Resource
      description: This should contain the kubernetes YAML manifest. You can only
        pass one manifest per resource. To add more manifests, use the Additional
        Resources section below.
      x-ui-yaml-editor: true
      x-ui-placeholder: Enter kubernetes manifest in YAML format
    additional_resources:
      type: object
      title: Additional Resources
      description: Additional resources that can be configured for k8s
      x-ui-title-replace: manifest
      patternProperties:
        "^[0-9]+[m]?$":
          type: object
          title: Additional resource name
          description: Define the k8s resource
          x-ui-allow-blueprint-merge: true
          properties:
            configuration:
              type: object
              title: Resource
              x-ui-ignore-parentkey: true
              description: This should contain the kubernetes YAML manifest. You can
                only pass one manifest per resource.
              x-ui-yaml-editor: true
              x-ui-placeholder: Enter kubernetes manifest in YAML format
  required:
  - resource
sample:
  $schema: >-
    https://facets-cloud.github.io/facets-schemas/schemas/k8s_resource/k8s_resource.schema.json
  kind: k8s_resource
  metadata: {}
  flavor: k8s
  version: "0.2"
  disabled: true
  spec:
    additional_resources: {}
    resource:
      apiVersion: v1
      kind: Service
      metadata:
        name: k8s-resource
        annotations:
          service.beta.kubernetes.io/aws-load-balancer-scheme: internal
          service.beta.kubernetes.io/aws-load-balancer-type: nlb
      spec:
        selector:
          app: facetsapi
        type: ClusterIP
        ports:
        - port: 80
          name: http
          targetPort: 80
