intent: network
flavor: aws_vpc
version: "0.1"
description: Adds network - aws vpc flavor
clouds:
- aws
outputs:
  default:
    type: "@outputs/aws_vpc"
spec:
  title: network
  description: Specification of the network for aws vpc flavor
  type: object
  properties:
    choose_vpc_type:
      title: Choose VPC Type
      description: Choose the type of vpc to create
      type: radio
      enum: [{label: Create New VPC, value: create_new_vpc}, {label: Use Existing
            VPC, value: use_existing_vpc}]
      default: create_new_vpc
      x-ui-immutable: true
      x-ui-overrides-only: true
    azs:
      title: Availability Zones
      description: Availability zones for the vpc
      type: array
      x-ui-placeholder: e.g. us-east-1a us-east-1b
      x-ui-immutable: true
      x-ui-overrides-only: true
      x-ui-fetch-az: true
      items:
        type: string
    vpc_cidr:
      title: VPC CIDR
      description: CIDR block for the vpc
      x-ui-placeholder: e.g. *********/16
      pattern: 
        ^(10.(\d{1,3}.){2}\d{1,3}/(2[0-8]|[89]|1[0-9])|172.(1[6-9]|2[0-9]|3[01]).(\d{1,3}.){1}\d{1,3}/(2[0-8]|[12][0-9])|192.168.(\d{1,3}.){1}\d{1,3}/(2[0-8]|[12][0-9]|1[6-9]))$
      x-ui-error-message: The provided CIDR block is invalid. Please ensure it is
        a valid private IP range in CIDR notation
      type: string
      x-ui-immutable: true
      x-ui-overrides-only: true
    enable_multi_az:
      title: Enable Multi AZ
      description: Enable multi availability zone network
      type: boolean
      x-ui-immutable: true
      x-ui-overrides-only: true
    existing_vpc_id:
      title: Existing VPC ID
      description: To setup network components in an existing vpc
      type: string
      x-ui-visible-if:
        field: spec.choose_vpc_type
        values: ["use_existing_vpc"]
      x-ui-immutable: true
      x-ui-overrides-only: true
  required: [azs, vpc_cidr, existing_vpc_id]
  x-ui-order: [choose_vpc_type, existing_vpc_id, azs, vpc_cidr, enable_multi_az]
sample:
  kind: network
  flavor: aws_vpc
  version: "0.1"
  lifecycle: ENVIRONMENT_NO_DEPS
  disabled: true
  depends_on: []
  metadata: {}
  spec: {}
