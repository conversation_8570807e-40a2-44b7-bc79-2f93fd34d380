intent: kafka
flavor: msk
version: "0.1"
description: Adds Kafka module of MSK flavor
clouds:
- aws
spec:
  title: Kafka spec
  type: object
  properties:
    authenticated:
      type: boolean
      title: Authenticated
      description: Make this kafka password protected
    kafka_version:
      type: string
      title: Kafka Version
      description: Version of kafka e.g. 3.2.3
      enum:
      - 1.1.1
      - 2.1.0
      - 2.2.1
      - 2.3.1
      - 2.4.1
      - *******
      - 2.5.1
      - 2.6.0
      - 2.6.1
      - 2.6.2
      - 2.6.3
      - 2.7.0
      - 2.7.1
      - 2.7.2
      - 2.8.0
      - 2.8.1
      - 2.8.2-tiered
      - 3.1.1
      - 3.2.0
      - 3.3.1
      - 3.3.2
      - 3.4.0
      - 3.5.1
      - 3.6.0
      - 3.7.1
      - 3.8.0
    persistence_enabled:
      type: boolean
      title: Persistence Enabled
      description: Enable Persistence for this redis
    size:
      type: object
      title: Size
      description: The size details
      properties:
        kafka:
          type: object
          title: Kafka
          description: The kafka details
          properties:
            instance:
              type: string
              title: Instance
              description: Kafka instance type
              enum:
              - kafka.t3.small
              - kafka.m5.large
              - kafka.m7g.large
              - kafka.m5.xlarge
              - kafka.m7g.xlarge
              - kafka.m5.2xlarge
              - kafka.m7g.2xlarge
              - kafka.m5.4xlarge
              - kafka.m7g.4xlarge
              - kafka.m5.8xlarge
              - kafka.m7g.8xlarge
              - kafka.m5.12xlarge
              - kafka.m7g.12xlarge
              - kafka.m5.16xlarge
              - kafka.m7g.16xlarge
            instance_count:
              type: integer
              title: Instance Count
              description: Number of kafka instances needs to be deployed
              minimum: 0
              maximum: 20
            volume:
              type: string
              title: Volume
              description: Volume request in kubernetes persistent volumes
              pattern: "^[0-9]+[G]i?$"
              x-ui-placeholder: "Enter volume size"
              x-ui-error-message: "Volume must be specified in the correct format
                with integer only (e.g., '10Gi' or '50Gi')."
              x-ui-visible-if:
                field: spec.persistence_enabled
                values: [true]
          required: ["instance", "instance_count", "volume"]
      required: ["kafka"]
  required: ["authenticated", "kafka_version", "persistence_enabled", "size"]
sample:
  $schema: "https://facets-cloud.github.io/facets-schemas/schemas/kafka/kafka.schema.json"
  kind: kafka
  flavor: msk
  version: "0.1"
  disabled: true
  metadata: {}
  spec:
    authenticated: true
    kafka_version: 2.8.0
    persistence_enabled: true
    size:
      kafka:
        instance: kafka.t3.small
        instance_count: 2
        volume: 10Gi
