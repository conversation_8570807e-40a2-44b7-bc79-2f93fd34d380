intent: artifactories
flavor: default
version: '0.1'
description: Adds artifactories - default flavor
clouds:
- gcp
- kubernetes
- azure
- aws
inputs:
  kubernetes_details:
    optional: false
    type: "@output/kubernetes"
    displayName: Kubernetes Cluster
    default:
      resource_type: kubernetes_cluster
      resource_name: default
spec:
  title: Artifactories
  type: object
  description: Specification of the Artifactories resource intent
  properties:
    include_all:
      type: boolean
      title: Include All
      description: "Include all container registries mapped to this project."
    artifactories:
      title: Artifactories
      type: object
      description: "A map of Container Registries."
      x-ui-visible-if:
        field: spec.include_all
        values: [false]
      patternProperties:
        "^[a-zA-Z0-9_.-]*$":
          title: Artifactories Object Block
          description: The Name of the Container Registry for creating a secret.
          type: object
          properties:
            name:
              title: Name
              type: string
              description: "Container Registry Name"
              x-ui-api-source:
                endpoint: "/cc-ui/v1/artifactories"
                method: GET
                params:
                  includeContent: false
                labelKey: name
                valueKey: name
sample:
  $schema: 
    https://facets-cloud.github.io/facets-schemas/schemas/artifactories/artifactories.schema.json
  version: '0.1'
  flavor: default
  kind: artifactories
  disabled: true
  spec:
    include_all: false
