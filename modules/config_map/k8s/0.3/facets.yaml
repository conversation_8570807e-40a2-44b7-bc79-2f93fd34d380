intent: config_map
flavor: k8s
version: '0.3'
description: config_map - k8s
clouds:
- aws
- azure
- gcp
- kubernetes
inputs:
  kubernetes_details:
    optional: false
    type: '@outputs/kubernetes'
    default:
      resource_type: kubernetes_cluster
      resource_name: default
    providers:
    - kubernetes
    - kubernetes-alpha
    - helm
spec:
  type: object
  properties:
    data:
      title: Data
      type: object
      description: Data objects containing key, value pair
      patternProperties:
        ^[a-zA-Z0-9_.-]*$:
          title: Data Object Block
          description: something
          type: object
          properties:
            key:
              title: ConfigMap Data Key Name
              description: Key name of the configmap object
              type: string
            value:
              title: ConfigMap Data Object Value
              description: Value of the configmap object
              type: string
              x-ui-textarea: true
sample:
  $schema: https://facets-cloud.github.io/facets-schemas/schemas/config_map/config_map.schema.json
  kind: config_map
  flavor: k8s
  version: '0.3'
  metadata: {}
  disabled: true
  spec:
    data: {}
iac:
  validated_files:
  - variables.tf
