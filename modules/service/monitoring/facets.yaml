intent: service_monitoring
flavor: k8s
version: "0.1"
clouds:
- aws
- kubernetes
- azure
- gcp
description: Adds Monitoring to Service
inputs:
  service_attributes:
    type: "@output/service"
    adds_capability: true
spec:
  title: Service Monitoring
  type: object
  properties:
    metrics:
      type: object
      title: Metrics
      description: Service Metrics Configuration
      patternProperties:
        "^[a-zA-Z0-9-_]*$":
          type: object
          title: Metrics Configuration
          description: Service Metrics Configuration
          properties:
            path:
              type: string
              title: Metrics Path
              description: Path Which Exposes the Metric
            port_name:
              type: string
              title: Metrics Port Name
              description: Service Port Name Which Exposes the Metric
            scrape_interval:
              type: string
              title: Scrape Interval
              description: Interval At Which The Metrics Should Be Scraped
              pattern: ^(\d+(\.\d+)?(h|m|s|ms|us|µs|ns))+$
              x-ui-placeholder: "eg. 1m5s"
              x-ui-error-message: The time duration must be in a valid format, such
                as 72h, 3.5m, 1.2s, 500ms, 2h50m, or 1m25s.
    alerts:
      type: object
      title: Alerts
      description: Alerts configuration
      properties:
        pod_pending_alert:
          type: object
          title: Pod Pending
          description: Pod Pending Alert
          properties:
            disabled:
              type: boolean
              title: Disabled
              description: Disable or Enable Alert
            interval:
              type: string
              title: Interval
              description: The duration an alert condition must persist before the
                alert is triggered
              pattern: ^(\d+(\.\d+)?(h|m|s|ms|us|µs|ns))+$
              x-ui-placeholder: "eg. 1m5s"
              x-ui-error-message: The time duration must be in a valid format, such
                as 72h, 3.5m, 1.2s, 500ms, 2h50m, or 1m25s.
            severity:
              type: string
              title: Severity
              description: The Severity to categorize the urgency or impact level
                of an alert, aiding in appropriate response prioritization
        pod_crashlooping_alert:
          type: object
          title: Pod Crashlooping
          description: Pod Crashlooping Alert
          properties:
            disabled:
              type: boolean
              title: Disabled
              description: Disable or Enable Alert
            interval:
              type: string
              title: Interval
              description: The duration an alert condition must persist before the
                alert is triggered
              pattern: ^(\d+(\.\d+)?(h|m|s|ms|us|µs|ns))+$
              x-ui-placeholder: "eg. 1m5s"
              x-ui-error-message: The time duration must be in a valid format, such
                as 72h, 3.5m, 1.2s, 500ms, 2h50m, or 1m25s.
            severity:
              type: string
              title: Severity
              description: The Severity to categorize the urgency or impact level
                of an alert, aiding in appropriate response prioritization
        invalid_image_name_alert:
          type: object
          title: Invalid Image Name
          description: Invalid Image Name Alert
          properties:
            disabled:
              type: boolean
              title: Disabled
              description: Disable or Enable Alert
            interval:
              type: string
              title: Interval
              description: The duration an alert condition must persist before the
                alert is triggered
              pattern: ^(\d+(\.\d+)?(h|m|s|ms|us|µs|ns))+$
              x-ui-placeholder: "eg. 1m5s"
              x-ui-error-message: The time duration must be in a valid format, such
                as 72h, 3.5m, 1.2s, 500ms, 2h50m, or 1m25s.
            severity:
              type: string
              title: Severity
              description: The Severity to categorize the urgency or impact level
                of an alert, aiding in appropriate response prioritization
        pod_waiting_alert:
          type: object
          title: Pod Waiting
          description: Pod Waiting Alert
          properties:
            disabled:
              type: boolean
              title: Disabled
              description: Disable or Enable Alert
            interval:
              type: string
              title: Interval
              description: The duration an alert condition must persist before the
                alert is triggered
              pattern: ^(\d+(\.\d+)?(h|m|s|ms|us|µs|ns))+$
              x-ui-placeholder: "eg. 1m5s"
              x-ui-error-message: The time duration must be in a valid format, such
                as 72h, 3.5m, 1.2s, 500ms, 2h50m, or 1m25s.
            severity:
              type: string
              title: Severity
              description: The Severity to categorize the urgency or impact level
                of an alert, aiding in appropriate response prioritization
        cpu_throttling_alert:
          type: object
          title: CPU Throttling
          description: CPU Throttling Alert
          properties:
            disabled:
              type: boolean
              title: Disabled
              description: Disable or Enable Alert
            interval:
              type: string
              title: Interval
              description: The duration an alert condition must persist before the
                alert is triggered
              pattern: ^(\d+(\.\d+)?(h|m|s|ms|us|µs|ns))+$
              x-ui-placeholder: "eg. 1m5s"
              x-ui-error-message: The time duration must be in a valid format, such
                as 72h, 3.5m, 1.2s, 500ms, 2h50m, or 1m25s.
            severity:
              type: string
              title: Severity
              description: The Severity to categorize the urgency or impact level
                of an alert, aiding in appropriate response prioritization
            threshold:
              type: integer
              title: Threshold
              description: The threshold cpu throttling percentage at which the alert
                is triggered
              minimum: 0
              maximum: 100
        high_memory_utilization_alert:
          type: object
          title: High Memory Utilization
          description: High Memory Utilization Alert
          properties:
            disabled:
              type: boolean
              title: Disabled
              description: Disable or Enable Alert
            interval:
              type: string
              title: Interval
              description: The duration an alert condition must persist before the
                alert is triggered
              pattern: ^(\d+(\.\d+)?(h|m|s|ms|us|µs|ns))+$
              x-ui-placeholder: "eg. 1m5s"
              x-ui-error-message: The time duration must be in a valid format, such
                as 72h, 3.5m, 1.2s, 500ms, 2h50m, or 1m25s.
            severity:
              type: string
              title: Severity
              description: The Severity to categorize the urgency or impact level
                of an alert, aiding in appropriate response prioritization
            threshold:
              type: string
              title: Threshold
              description: The threshold memory utilization percentage at which the
                alert is triggered
              minimum: 0
              maximum: 100
        pod_not_ready_alert:
          type: object
          title: Pod Not Ready
          description: Pod Not Ready Alert
          properties:
            disabled:
              type: boolean
              title: Disabled
              description: Disable or Enable Alert
            interval:
              type: string
              title: Interval
              description: The duration an alert condition must persist before the
                alert is triggered
              pattern: ^(\d+(\.\d+)?(h|m|s|ms|us|µs|ns))+$
              x-ui-placeholder: "eg. 1m5s"
              x-ui-error-message: The time duration must be in a valid format, such
                as 72h, 3.5m, 1.2s, 500ms, 2h50m, or 1m25s.
            severity:
              type: string
              title: Severity
              description: The Severity to categorize the urgency or impact level
                of an alert, aiding in appropriate response prioritization
        pod_evicted_alert:
          type: object
          title: Pod Evicted
          description: Pod Evicted Alert
          properties:
            disabled:
              type: boolean
              title: Disabled
              description: Disable or Enable Alert
            interval:
              type: string
              title: Interval
              description: The duration an alert condition must persist before the
                alert is triggered
              pattern: ^(\d+(\.\d+)?(h|m|s|ms|us|µs|ns))+$
              x-ui-placeholder: "eg. 1m5s"
              x-ui-error-message: The time duration must be in a valid format, such
                as 72h, 3.5m, 1.2s, 500ms, 2h50m, or 1m25s.
            severity:
              type: string
              title: Severity
              description: The Severity to categorize the urgency or impact level
                of an alert, aiding in appropriate response prioritization
        failed_http_requests_alert:
          type: object
          title: Failed HTTP Requests
          description: Failed HTTP Requests Alert
          properties:
            disabled:
              type: boolean
              title: Disabled
              description: Disable or Enable Alert
            interval:
              type: string
              title: Interval
              description: The duration an alert condition must persist before the
                alert is triggered
              pattern: ^(\d+(\.\d+)?(h|m|s|ms|us|µs|ns))+$
              x-ui-placeholder: "eg. 1m5s"
              x-ui-error-message: The time duration must be in a valid format, such
                as 72h, 3.5m, 1.2s, 500ms, 2h50m, or 1m25s.
            severity:
              type: string
              title: Severity
              description: The Severity to categorize the urgency or impact level
                of an alert, aiding in appropriate response prioritization
            threshold:
              type: integer
              title: Threshold
              description: The threshold percentage at which the alert is triggered
              minimum: 0
              maximum: 100
        ingress_endpoints_alert:
          type: object
          title: Ingress Endpoints
          description: Ingress Endpoints Not Active Alert
          properties:
            disabled:
              type: boolean
              title: Disabled
              description: Disable or Enable Alert
            interval:
              type: string
              title: Interval
              description: The duration an alert condition must persist before the
                alert is triggered
              pattern: ^(\d+(\.\d+)?(h|m|s|ms|us|µs|ns))+$
              x-ui-placeholder: "eg. 1m5s"
              x-ui-error-message: The time duration must be in a valid format, such
                as 72h, 3.5m, 1.2s, 500ms, 2h50m, or 1m25s.
            severity:
              type: string
              title: Severity
              description: The Severity to categorize the urgency or impact level
                of an alert, aiding in appropriate response prioritization
        service_vpa_cpu:
          type: object
          title: Vertical Pod Autoscaler CPU
          description: Vertical Pod Autoscaler CPU Alert
          properties:
            disabled:
              type: boolean
              title: Disabled
              description: Disable or Enable Alert
            interval:
              type: string
              title: Interval
              description: The duration an alert condition must persist before the
                alert is triggered
              pattern: ^(\d+(\.\d+)?(h|m|s|ms|us|µs|ns))+$
              x-ui-placeholder: "eg. 1m5s"
              x-ui-error-message: The time duration must be in a valid format, such
                as 72h, 3.5m, 1.2s, 500ms, 2h50m, or 1m25s.
            severity:
              type: string
              title: Severity
              description: The Severity to categorize the urgency or impact level
                of an alert, aiding in appropriate response prioritization
            threshold:
              type: string
              title: Threshold
              description: The threshold percentage at which the alert is triggered

        service_vpa_memory:
          type: object
          title: Vertical Pod Autoscaler Memory
          description: Vertical Pod Autoscaler Memory Alert
          properties:
            disabled:
              type: boolean
              title: Disabled
              description: Disable or Enable Alert
            interval:
              type: string
              title: Interval
              description: The duration an alert condition must persist before the
                alert is triggered
              pattern: ^(\d+(\.\d+)?(h|m|s|ms|us|µs|ns))+$
              x-ui-placeholder: "eg. 1m5s"
              x-ui-error-message: The time duration must be in a valid format, such
                as 72h, 3.5m, 1.2s, 500ms, 2h50m, or 1m25s.
            severity:
              type: string
              title: Severity
              description: The Severity to categorize the urgency or impact level
                of an alert, aiding in appropriate response prioritization
            threshold:
              type: string
              title: Threshold
              description: The threshold percentage at which the alert is triggered
sample:
  kind: service_monitoring
  flavor: k8s
  version: "0.1"
  disabled: true
  metadata: {}
  spec:
    metrics:
      metric1:
        path: /metrics
        port_name: metrics
        scrape_interval: 30s
    alerts:
      pod_pending_alert:
        disabled: false
        interval: 15m
        severity: critical
      pod_crashlooping_alert:
        disabled: false
        interval: 15m
        severity: critical
      invalid_image_name_alert:
        disabled: false
        interval: 15m
        severity: critical
      pod_waiting_alert:
        disabled: false
        interval: 15m
        severity: critical
      cpu_throttling_alert:
        disabled: false
        interval: 15m
        severity: critical
        threshold: 25
      high_memory_utilization_alert:
        disabled: false
        interval: 15m
        severity: warning
        threshold: 95
      pod_not_ready_alert:
        disabled: false
        interval: 15m
        severity: critical
      pod_evicted_alert:
        disabled: false
        interval: 0m
        severity: critical
      failed_http_requests_alert:
        disabled: false
        interval: 15m
        severity: critical
        threshold: 25
      ingress_endpoints_alert:
        disabled: false
        interval: 5m
        severity: critical
      service_vpa_cpu:
        disabled: false
        interval: 60m
        severity: critical
        threshold: 0.25
      service_vpa_memory:
        disabled: false
        interval: 60m
        severity: critical
        threshold: 0.25
