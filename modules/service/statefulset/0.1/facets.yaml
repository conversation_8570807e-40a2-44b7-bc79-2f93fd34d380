intent: service
flavor: statefulset
version: "0.1"
description: Adds service module
clouds:
- aws
- azure
- gcp
- kubernetes
artifact_inputs:
  primary:
    attribute_path: "spec.release.image"
    artifact_type: "docker_image"
metadata:
  title: Metadata of service
  type: object
  properties:
    namespace:
      type: string
      title: Namespace
      description: Namespace in which statefulset should be deployed
      default: default
spec:
  title: K8s flavor of service
  type: object
  properties:
    type:
      type: string
      title: Service Type
      description: Type - Statefulset
      x-ui-override-disable: true
      default: statefulset
    persistent_volume_claims:
      type: object
      title: Persistent Volume Claim
      Description: PVCs that you will create and mount to your statefulset
      x-ui-override-disable: true
      x-ui-toggle: true
      patternProperties:
        "^[a-zA-Z0-9_-]*$":
          type: object
          title: PVC
          description: Define volume details
          properties:
            access_mode:
              title: Access Mode
              type: string
              enum:
              - ReadWriteOnce
              - ReadWriteMany
              description: Define read-write access of the pvc
            storage_size:
              type: string
              title: Storage Size
              description: The storage size of the pvc , it should be specified like
                `10Gi`
              minLength: 1
              pattern: "^(0\\.[1-9]|[1-9](\\.[0-9]+)?|[1-5][0-9](\\.[0-9]+)?|6[0-4])Gi$|^([1-9](\\\
                .[0-9]+)?|[1-9][0-9]{1,3}(\\.[0-9]+)?|[1-5][0-9]{4}(\\.[0-9]+)?|6[0-3][0-9]{3}(\\\
                .[0-9]+)?|64000)Mi$"
              x-ui-placeholder: "e.g., '800Mi' or '1.5Gi'"
              x-ui-error-message: "Value doesn't match pattern, it should be number
                ranging from 1Gi to 64Gi or 1Mi to 64000Mi"
            path:
              type: string
              title: Path
              description: Path to mount the PVC
              pattern: "^(/[^/]+)*(/)?$"
              x-ui-error-message: "Value doesn't match pattern, eg: / or /<path> etc"
              x-ui-placeholder: "Enter the storage size, eg. /data/temp"
          required:
          - access_mode
          - path
          - storage_size
    enable_host_anti_affinity:
      type: boolean
      title: Enable Host Anti-Affinity
      description: Distribute instances across different physical hosts within cluster
    cloud_permissions:
      type: object
      title: Cloud Permissions
      description: Assign roles, define access levels, and enforce conditions for
        managing resource security and compliance
      x-ui-toggle: true
      properties:
        aws:
          type: object
          title: AWS
          x-ui-toggle: true
          properties:
            enable_irsa:
              type: boolean
              title: Enable IRSA
              description: Enable this to grant fine-grained AWS permissions to Kubernetes
                workloads using service accounts.
            iam_policies:
              type: object
              title: IAM policies
              description: Define IAM policies to manage permissions, control-access
                and secure resources
              patternProperties:
                "^[a-zA-Z0-9_.-]*$":
                  title: policy_name
                  properties:
                    arn:
                      title: ARN
                      type: string
                      pattern: '^(arn:aws:iam::(\d{12}|aws):policy\/[A-Za-z0-9+=,.@\-_]+|\$\{[A-Za-z0-9._-]+\})$'
                      x-ui-placeholder: "IAM policy example. Eg: arn:aws:iam::************:policy/policy1"
                      x-ui-error-message: "Value doesn't match pattern, accepted value
                        pattern Eg: arn:aws:iam::************:policy/policy1"
                      x-ui-output-type: "iam_policy_arn"
                      x-ui-typeable: true
                  required:
                  - arn
                  type: object
        gcp:
          type: object
          title: GCP
          x-ui-toggle: true
          properties:
            roles:
              type: object
              title: GCP Role
              description: Assign GCP roles to define access and permissions for managing
                resources.
              patternProperties:
                "^[a-zA-Z0-9_.-]*$":
                  title: Roles
                  properties:
                    role:
                      title: Role
                      type: string
                      pattern: '(^roles\/[A-Za-z_.-]+$)|(^\${.+}$)'
                      x-ui-placeholder: "gcp role example. Eg: roles/firestore.serviceAgent"
                      x-ui-error-message: "Value doesn't match pattern, accepted value
                        pattern Eg: roles/firestore.serviceAgent"
                    condition:
                      type: object
                      title: Condition
                      x-ui-toggle: true
                      description: Define conditional logic to grant the role based
                        on specific attributes or criteria, such as time or request
                        context.
                      properties:
                        expression:
                          title: Expression
                          type: string
                        title:
                          title: Title
                          type: string
                        description:
                          title: Description
                          type: string
                      required:
                      - expression
                  required:
                  - role
                  type: object
        azure:
          type: object
          title: Azure
          x-ui-toggle: true
          properties:
            roles:
              type: object
              title: Azure Role
              description: Assign Azure roles to define access and permissions for
                managing resources.
              patternProperties:
                "^[a-zA-Z0-9_.-]*$":
                  title: Roles
                  properties:
                    role:
                      title: role
                      type: string
                      x-ui-placeholder: "Azure role to be added, example. Eg: Storage
                        Account Contributor"
                  required:
                  - role
                  type: object
    runtime:
      type: object
      title: Runtime (Main Application Container)
      description: Customize service runtime settings like ports, healthchecks etc.
      properties:
        command:
          type: array
          title: Command
          description: Command to run in the container
          items:
            type: string
          x-ui-command: true
          x-ui-override-disable: true
        args:
          type: array
          title: Arguments
          description: Arguments to pass to the command
          items:
            type: string
          x-ui-command: true
          x-ui-override-disable: true
        health_checks:
          type: object
          title: Health Checks
          description: Ensure service remains responsive and operational. Assess its
            status and performance.
          x-ui-override-disable: true
          x-ui-toggle: true
          properties:
            readiness_check_type:
              type: string
              title: Readiness Check Type
              default: None
              x-ui-no-sort: true
              enum:
              - None
              - PortCheck
              - HttpCheck
              - ExecCheck
            readiness_start_up_time:
              type: integer
              title: Readiness Start-Up Time
              default: 10
              minimum: 0
              maximum: 10000
              x-ui-placeholder: "Enter readiness start up time for the Pod"
              x-ui-error-message: "Value doesn't match pattern, accepted values are
                from 0-10000"
              x-ui-visible-if:
                field: spec.runtime.health_checks.readiness_check_type
                values: ["PortCheck", "HttpCheck", "ExecCheck"]
              description: Wait time for readiness check (in sec)
            readiness_timeout:
              type: integer
              title: Readiness Timeout
              default: 10
              minimum: 0
              maximum: 10000
              x-ui-placeholder: "Enter readiness timeout for the Pod"
              x-ui-error-message: "Value doesn't match pattern, accepted values are
                from 0-10000"
              x-ui-visible-if:
                field: spec.runtime.health_checks.readiness_check_type
                values: ["PortCheck", "HttpCheck", "ExecCheck"]
              description: Max time for readiness (in sec)
            readiness_period:
              type: integer
              title: Readiness Check Interval
              default: 10
              minimum: 0
              maximum: 10000
              x-ui-placeholder: "Enter readiness period for the Pod"
              x-ui-error-message: "Value doesn't match pattern, accepted values are
                from 0-10000"
              x-ui-visible-if:
                field: spec.runtime.health_checks.readiness_check_type
                values: ["PortCheck", "HttpCheck", "ExecCheck"]
              description: Repeated interval for health-check (in sec)
            readiness_port:
              type: string
              title: Readiness Port
              x-ui-placeholder: "Enter readiness port for the Pod"
              x-ui-error-message: "Value doesn't match pattern, accepted values are
                from 0-65535"
              x-ui-visible-if:
                field: spec.runtime.health_checks.readiness_check_type
                values: ["PortCheck", "HttpCheck"]
              description: Port for Health Checks
              x-ui-dynamic-enum: spec.runtime.ports.*.port
              x-ui-disable-tooltip: "No Ports Added"
            readiness_url:
              type: string
              title: Readiness URL
              pattern: "^(/[^/]+)*(/)?$"
              x-ui-placeholder: "Enter readiness url for the Pod"
              x-ui-error-message: "Value doesn't match pattern, eg: / , /<path>"
              x-ui-visible-if:
                field: spec.runtime.health_checks.readiness_check_type
                values: ["HttpCheck"]
              description: URL for readiness
            readiness_exec_command:
              type: array
              title: Readiness Exec Command
              pattern: '^([^,]+)(,\s*[^,]+)*$'
              x-ui-placeholder: "Enter readiness exec for the Pod, commands are separated
                by commas"
              x-ui-error-message: "Value doesn't match pattern, commands needs to
                be comma separated"
              x-ui-visible-if:
                field: spec.runtime.health_checks.readiness_check_type
                values: ["ExecCheck"]
              description: Commands for readiness check
              items:
                type: string
              x-ui-override-disable: true
            liveness_check_type:
              type: string
              title: Liveness Check Type
              default: None
              x-ui-no-sort: true
              enum:
              - None
              - PortCheck
              - HttpCheck
              - ExecCheck
            liveness_start_up_time:
              type: integer
              default: 10
              title: Liveness Start-Up Time
              minimum: 0
              maximum: 10000
              x-ui-placeholder: "Enter liveness start up time for the Pod"
              x-ui-error-message: "Value doesn't match pattern, accepted values are
                from 0-10000"
              x-ui-visible-if:
                field: spec.runtime.health_checks.liveness_check_type
                values: ["PortCheck", "HttpCheck", "ExecCheck"]
              description: Wait time for liveness (in sec)
            liveness_timeout:
              type: integer
              default: 10
              title: Liveness Timeout
              minimum: 0
              maximum: 10000
              x-ui-placeholder: "Enter liveness timeout for the Pod"
              x-ui-error-message: "Value doesn't match pattern, accepted values are
                from 0-10000"
              x-ui-visible-if:
                field: "spec.runtime.health_checks.liveness_check_type"
                values: ["PortCheck", "HttpCheck", "ExecCheck"]
              description: Max time for liveness (in sec)
            liveness_period:
              type: integer
              title: Liveness Check Interval
              default: 10
              minimum: 0
              maximum: 10000
              x-ui-placeholder: "Enter liveness period for the Pod"
              x-ui-error-message: "Value doesn't match pattern, accepted values are
                from 0-10000"
              x-ui-visible-if:
                field: "spec.runtime.health_checks.liveness_check_type"
                values: ["PortCheck", "HttpCheck", "ExecCheck"]
              description: Interval for Liveness (in sec)
            liveness_port:
              type: string
              title: Liveness Port
              x-ui-placeholder: "Enter liveness port for the Pod"
              x-ui-error-message: "Value doesn't match pattern, accepted values are
                from 0-65535"
              x-ui-visible-if:
                field: "spec.runtime.health_checks.liveness_check_type"
                values: ["PortCheck", "HttpCheck"]
              description: Port for Health Checks
              x-ui-dynamic-enum: spec.runtime.ports.*.port
              x-ui-disable-tooltip: "No Ports Added"
            liveness_url:
              type: string
              title: Liveness URL
              pattern: "^(/[^/]+)*(/)?$"
              x-ui-placeholder: "Enter liveness url for the Pod"
              x-ui-error-message: "Value doesn't match pattern, eg: / , /<path>"
              x-ui-visible-if:
                field: "spec.runtime.health_checks.liveness_check_type"
                values: ["HttpCheck"]
              description: URL for liveness
            liveness_exec_command:
              type: array
              title: Liveness Exec Command
              pattern: '^([^,]+)(,\s*[^,]+)*$'
              x-ui-placeholder: "Enter readiness exec for the Pod, commands are separated
                by commas"
              x-ui-error-message: "Value doesn't match pattern, commands needs to
                be comma separated"
              x-ui-visible-if:
                field: "spec.runtime.health_checks.liveness_check_type"
                values: ["ExecCheck"]
              description: Commands for liveness
              items:
                type: string
              x-ui-override-disable: true
          required:
          - readiness_port
          - readiness_url
          - readiness_exec_command
          - liveness_port
          - liveness_url
          - liveness_exec_command
        autoscaling:
          type: object
          title: Autoscaling
          description: Automatically adjust resources based on demand for optimal
            performance
          x-ui-toggle: true
          properties:
            min:
              type: integer
              title: Min
              default: 1
              minimum: 1
              maximum: 200
              description: Min replicas
              x-ui-placeholder: "Enter Min replicas for you application"
              x-ui-error-message: "Value doesn't match pattern, number should be ranging
                from 1-200"
            max:
              type: integer
              title: Max
              default: 1
              minimum: 1
              maximum: 200
              description: Max replicas
              x-ui-placeholder: "Enter Max replicas for you application"
              x-ui-validation: "spec.runtime.autoscaling.max > spec.runtime.autoscaling.min"
              x-ui-error-message: "Value doesn't match pattern, number should be ranging
                from 1-200 and Max should be more than Min"
            scaling_on:
              type: string
              title: Scale based on
              default: CPU
              enum:
              - CPU
              - RAM
            cpu_threshold:
              type: string
              title: CPU Threshold
              description: Max CPU threshold
              pattern: "^(100|[1-9][0-9]?)$"
              x-ui-placeholder: "Enter cpu threshold for you application to scale"
              x-ui-error-message: "Value doesn't match pattern, number should be ranging
                from 1-100"
              x-ui-visible-if:
                field: "spec.runtime.autoscaling.scaling_on"
                values: ["CPU"]
            ram_threshold:
              type: string
              title: RAM Threshold
              description: Max RAM threshold
              pattern: "^(1?[0-9]?[0-9]|100)$"
              x-ui-placeholder: "Enter memory threshold for you application to scale"
              x-ui-error-message: "Value doesn't match pattern, number should be ranging
                from 1-100"
              x-ui-visible-if:
                field: "spec.runtime.autoscaling.scaling_on"
                values: ["RAM"]
        size:
          type: object
          title: Size
          description: Configure container size to meet resource requirements
          properties:
            cpu:
              type: string
              title: CPU
              description: CPU size
              pattern: "^([1-9]|[12][0-9]|3[0-2])$|^(3[0-1][0-9]{3}|[1-2][0-9]{4}|[1-9][0-9]{0,3}|32000)m$"
              x-ui-compare:
                field: spec.runtime.size.cpu_limit
                comparator: '<='
                x-ui-error-message: 'CPU cannot be more than CPU limit'
              x-ui-placeholder: "Enter CPU requests for your application"
              x-ui-error-message: "Value doesn't match pattern, it should be number
                ranging from 1 to 32 or 1m to 32000m"
            memory:
              type: string
              title: Memory
              description: Memory size
              pattern: "^([1-9]|[1-5][0-9]|6[0-4])Gi$|^([1-9]|[1-9][0-9]{1,3}|[1-5][0-9]{4}|6[0-3][0-9]{3}|64000)Mi$"
              x-ui-compare:
                field: spec.runtime.size.memory_limit
                comparator: '<='
                x-ui-error-message: 'Memory cannot be more than memory limit'
              x-ui-placeholder: "Enter Memory requests for your application"
              x-ui-error-message: "Value doesn't match pattern, it should be number
                ranging from 1Gi to 64Gi or 1Mi to 64000Mi"
            cpu_limit:
              type: string
              title: CPU Limit
              description: CPU limit size
              pattern: "^([1-9]|[12][0-9]|3[0-2])$|^(3[0-1][0-9]{3}|[1-2][0-9]{4}|[1-9][0-9]{0,3}|32000)m$"
              x-ui-compare:
                field: spec.runtime.size.cpu
                comparator: '>='
                x-ui-error-message: 'CPU limit cannot be less than CPU'
              x-ui-placeholder: "Enter CPU limits for your application"
              x-ui-error-message: "Value doesn't match pattern, it should be number
                ranging from 1 to 32 or 1m to 32000m"
            memory_limit:
              type: string
              title: Memory Limit
              description: Memory limit size
              pattern: "^([1-9]|[1-5][0-9]|6[0-4])Gi$|^([1-9]|[1-9][0-9]{1,3}|[1-5][0-9]{4}|6[0-3][0-9]{3}|64000)Mi$"
              x-ui-compare:
                field: spec.runtime.size.memory
                comparator: '>='
                x-ui-error-message: 'Memory limit cannot be less than memory'
              x-ui-placeholder: "Enter Memory limits for your application"
              x-ui-error-message: "Value doesn't match pattern, it should be number
                ranging from 1Gi to 64Gi or 1Mi to 64000Mi"
          required:
          - cpu
          - memory
        ports:
          type: object
          title: Ports
          x-ui-override-disable: true
          description: Port mappings
          patternProperties:
            "^[0-9]+[m]?$":
              type: object
              title: Port Name
              description: Define port allocation to facilitate communication with
                service
              properties:
                port:
                  type: string
                  title: Port
                  description: Port on which application is running
                  pattern: "^(?!0$)([1-9][0-9]{0,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])$"
                  x-ui-unique: true
                  x-ui-placeholder: "Enter the port number to expose for your application
                    container"
                  x-ui-error-message: "Value doesn't match pattern, it should be number
                    ranging from 1-65535"
                service_port:
                  type: string
                  title: Service Port
                  description: Port from which application service can be exposed
                  pattern: "^(?!0$)([1-9][0-9]{0,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])$"
                  x-ui-unique: true
                  x-ui-placeholder: "Enter the port number to expose for your application
                    service"
                  x-ui-error-message: "Value doesn't match pattern, it should be number
                    ranging from 1-65535"
                protocol:
                  type: string
                  title: Protocol
                  description: TCP or UDP
                  enum:
                  - tcp
                  - udp
              required:
              - protocol
              - port
        metrics:
          type: object
          title: Metrics
          x-ui-override-disable: true
          x-ui-toggle: true
          description: Metrics port mappings
          patternProperties:
            "^[a-zA-Z0-9_.-]*$":
              type: object
              title: Metrics Name
              description: Track and analyze performance metrics to monitor service
                health and efficiency
              properties:
                path:
                  type: string
                  title: Path
                  description: Metrics path
                  pattern: "^(/[^/]+)*(/)?$"
                port_name:
                  type: string
                  title: Port Name
                  description: Port name for metrics
                  x-ui-dynamic-enum: spec.runtime.ports.*
                  x-ui-disable-tooltip: "No Ports Available"
              required:
              - path
              - port_name
        volumes:
          type: object
          title: Volumes
          x-ui-override-disable: true
          x-ui-toggle: true
          description: Manage data storage volumes to ensure access and persistence
          properties:
            config_maps:
              type: object
              title: Config Maps
              description: Config map mounts
              patternProperties:
                "^[a-zA-Z0-9_.-]*$":
                  type: object
                  title: Config Map Name
                  properties:
                    name:
                      type: string
                      title: Name
                      pattern: "^[a-zA-Z0-9]([a-zA-Z0-9-]{1,18}[a-zA-Z0-9])$"
                      description: Config map name
                      x-ui-error-message: "Value doesn't match pattern, it should
                        start with an alphanumeric character and should be in range
                        of 3-20 characters"
                      x-ui-typeable: true
                      x-ui-api-source:
                        endpoint: "/cc-ui/v1/dropdown/stack/{{stackName}}/resources-info"
                        method: GET
                        params:
                          includeContent: false
                        labelKey: resourceName
                        valueKey: resourceName
                        valueTemplate: "${config_map.{{value}}.out.attributes.name}"
                        filterConditions:
                        - field: resourceType
                          value: config_map
                    mount_path:
                      type: string
                      title: Mount Path
                      pattern: "^/.*"
                      description: Mount path for config maps
                      x-ui-error-message: "Value doesn't match pattern, it should
                        start with a forward slash (/)"
                    sub_path:
                      type: string
                      title: Sub Path
                      description: Path within the ConfigMap to mount a specific key
                        or file
                  required:
                  - name
                  - mount_path
            secrets:
              type: object
              title: Secrets
              description: Secret mounts
              patternProperties:
                "^[a-zA-Z0-9_.-]*$":
                  type: object
                  title: Secret name
                  properties:
                    name:
                      type: string
                      title: Name
                      description: Secret name
                      x-ui-typeable: true
                      x-ui-api-source:
                        endpoint: "/cc-ui/v1/dropdown/stack/{{stackName}}/resources-info"
                        method: GET
                        params:
                          includeContent: false
                        labelKey: resourceName
                        valueKey: resourceName
                        valueTemplate: "${kubernetes_secret.{{value}}.out.attributes.name}"
                        filterConditions:
                        - field: resourceType
                          value: kubernetes_secret
                    mount_path:
                      type: string
                      title: Mount Path
                      pattern: "^/.*"
                      description: Mount path for secrets
                    sub_path:
                      type: string
                      title: Sub Path
                      description: Path within the Secret to mount a specific key
                        or file
                  required:
                  - name
                  - mount_path
            pvc:
              type: object
              title: PVC
              description: PVC mounts
              patternProperties:
                "^[a-zA-Z0-9_.-]*$":
                  type: object
                  title: PVC Name
                  properties:
                    claim_name:
                      type: string
                      title: Claim Name
                      pattern: "^[a-zA-Z0-9]([a-zA-Z0-9-]{1,18}[a-zA-Z0-9])$"
                      description: PVC name
                      x-ui-error-message: "Value doesn't match pattern, it should
                        start with an alphanumeric character and should be in range
                        of 3-20 characters"
                      x-ui-typeable: true
                      x-ui-api-source:
                        endpoint: "/cc-ui/v1/dropdown/stack/{{stackName}}/resources-info"
                        method: GET
                        params:
                          includeContent: false
                        labelKey: resourceName
                        valueKey: resourceName
                        valueTemplate: "${pvc.{{value}}.out.attributes.name}"
                        filterConditions:
                        - field: resourceType
                          value: pvc
                    mount_path:
                      type: string
                      title: Mount Path
                      pattern: "^/.*"
                      description: Mount path for PVC
                      x-ui-error-message: "Value doesn't match pattern, it should
                        start with a forward slash (/)"
                    sub_path:
                      type: string
                      title: Sub Path
                      description: Path within the PVC to mount a specific subdirectory
                  required:
                  - claim_name
                  - mount_path
            host_path:
              type: object
              title: Host Path
              description: Host path mounts
              patternProperties:
                "^[a-zA-Z0-9_.-]*$":
                  type: object
                  title: Host Path Name
                  properties:
                    mount_path:
                      type: string
                      title: Mount Path
                      pattern: "^/.*"
                      description: Mount path for host path
                      x-ui-error-message: "Value doesn't match pattern, it should
                        start with a forward slash (/)"
                    sub_path:
                      type: string
                      title: Sub Path
                      description: Path within the host filesystem to mount a specific
                        directory or file
                  required:
                  - name
                  - mount_path
      required:
      - ports
      - size
      - health_checks
      x-ui-order:
      - ports
      - metrics
      - size
      - health_checks
      - autoscaling
      - volumes
      - command
      - args
    release:
      title: Release
      description: Manage version releases to deploy updates and maintain software
        integrity
      type: object
      properties:
        image:
          type: string
          title: Image
          description: The docker image of the application that you want to run
          x-ui-skip: true
          x-ui-error-message: "Value doesn't match pattern for a container image"
        image_pull_policy:
          type: string
          title: Image Pull Policy
          description: Specifies when the image should be pulled from the registry.
          default: IfNotPresent
          enum:
          - IfNotPresent
          - Always
          - Never
        build:
          type: object
          title: Build
          description: These contains the build objects required for running the containers
          properties:
            artifactory:
              type: string
              title: Artifactory
              description: Parent artifactory name
            name:
              type: string
              title: Name
              description: Name of the artifactory
            pull_policy:
              type: string
              title: Pull Policy
              description: ImagePullPolicy
              enum:
              - IfNotPresent
              - Always
              - Never
          required: ["artifactory", "name"]
          x-ui-skip: true
        strategy:
          type: object
          title: Strategy
          description: The type of upgrade strategy to be followed by this service
          properties:
            type:
              type: string
              title: Type
              description: "Your kubernetes rollout type eg: RollingUpdate or Recreate"
              enum:
              - RollingUpdate
              - Recreate
            max_available:
              type: string
              title: Max Available
              description: If type RollingUpdate, this is the max number of pods that
                can be created from the default replicas
              pattern: "^((100|[1-9]?[0-9])%?)$"
              x-ui-placeholder: "Enter the max number or percentage of pods that needs
                to be available during rolling restart"
              x-ui-error-message: "Value doesn't match pattern, it should be number
                ranging from 0 to 100 or 0% to 100%"
            max_unavailable:
              type: string
              title: Max Unavailable
              description: If type RollingUpdate, this is the max number of pods that
                can be unavailable from the default replicas
              pattern: "^((100|[1-9]?[0-9])%?)$"
              x-ui-placeholder: "Enter the max number or percentage of pods that needs
                to be unavailable during rolling restart"
              x-ui-error-message: "Value doesn't match pattern, it should be number
                ranging from 0 to 100 or 0% to 100%"
        disruption_policy:
          type: object
          title: Disruption Policy
          description: The disruption policy for the service. Both min available and
            max unavailable cannot be specified simultaneously in disruption policy.
            You must fill one of the two fields when defining a disruption policy
            to avoid release failures.
          properties:
            min_available:
              type: string
              title: Min Available
              description: This is the min number of pods should be available in case
                of failures
              pattern: "^((100|[1-9]?[0-9])%?)$"
              x-ui-placeholder: "Enter the min number or percentage of pods that needs
                to be available during disruption, this is pod disruption budget"
              x-ui-error-message: "Value doesn't match pattern, it should be number
                ranging from 0 to 100 or 0% to 100%"
            max_unavailable:
              type: string
              title: Max Unavailable
              description: This is the max number of pods that can be unavailable
                during a failure.
              pattern: "^((100|[1-9]?[0-9])%?)$"
              x-ui-placeholder: "Enter the max number or percentage of pods that needs
                to be unavailable during disruption, this is pod disruption budget"
              x-ui-error-message: "Value doesn't match pattern, it should be number
                ranging from 0 to 100 or 0% to 100%"
    init_containers:
      title: Init Containers
      description: Map of specialized containers that run before app containers in
        a Pod. Init containers can contain utilities or setup scripts not present
        in an app image.
      type: object
      x-ui-toggle: true
      patternProperties:
        "^[a-z]([-a-z0-9]{0,61}[a-z0-9])?$":
          title: Init Container
          description: Init Container Specification.
          type: object
          properties:
            image:
              type: string
              title: Image
              description: Docker image of the init container.
              x-ui-error-message: "Value doesn't match pattern for a container image"
            pull_policy:
              title: Image Pull Policy
              description: Specifies when the image should be pulled from the registry.
              type: string
              default: IfNotPresent
              enum:
              - IfNotPresent
              - Always
              - Never
            env:
              title: Environment Variables
              description: Map of environment variables passed to init container.
              type: object
              patternProperties:
                "^[a-zA-Z][a-zA-Z0-9_-.]*$":
                  title: Evironment Variable
                  description: Environment Variable
                  type: string
              x-ui-yaml-editor: true
            additional_k8s_env:
              title: Additional Environment Variables
              description: List of evironment variables to be created from referencing
                key from configmap or secret. Eg 
                https://raw.githubusercontent.com/kubernetes/website/main/content/en/examples/pods/pod-configmap-env-var-valueFrom.yaml.
              type: object
              x-ui-yaml-editor: true
            additional_k8s_env_from:
              title: Import Additional Environment Variables
              description: List of configmap or secret reference from which environments
                varibales to be imported. Eg. 
                https://raw.githubusercontent.com/kubernetes/website/main/content/en/examples/pods/inject/pod-secret-envFrom.yaml.
              type: object
              x-ui-yaml-editor: true
            runtime:
              type: object
              title: Runtime
              description: Customize runtime settings like size, healthchecks etc.
              properties:
                command:
                  type: array
                  title: Command
                  description: Command to run in the container
                  items:
                    type: string
                  x-ui-command: true
                  x-ui-override-disable: true
                args:
                  type: array
                  title: Arguments
                  description: Arguments to pass to the command
                  items:
                    type: string
                  x-ui-command: true
                  x-ui-override-disable: true
                size:
                  type: object
                  title: Size
                  description: Configure container size to meet resource requirements.
                  properties:
                    cpu:
                      type: string
                      title: CPU
                      description: CPU size
                      pattern: "^([1-9]|[12][0-9]|3[0-2])$|^(3[0-1][0-9]{3}|[1-2][0-9]{4}|[1-9][0-9]{0,3}|32000)m$"
                      x-ui-placeholder: "Enter CPU requests for your application"
                      x-ui-error-message: "Value doesn't match pattern, it should
                        be number ranging from 1 to 32 or 1m to 32000m"
                    memory:
                      type: string
                      title: Memory
                      description: Memory size
                      pattern: "^([1-9]|[1-5][0-9]|6[0-4])Gi$|^([1-9]|[1-9][0-9]{1,3}|[1-5][0-9]{4}|6[0-3][0-9]{3}|64000)Mi$"
                      x-ui-placeholder: "Enter Memory requests for your application"
                      x-ui-error-message: "Value doesn't match pattern, it should
                        be number ranging from 1Gi to 64Gi or 1Mi to 64000Mi"
                    cpu_limit:
                      type: string
                      title: CPU Limit
                      description: CPU limit size
                      pattern: "^([1-9]|[12][0-9]|3[0-2])$|^(3[0-1][0-9]{3}|[1-2][0-9]{4}|[1-9][0-9]{0,3}|32000)m$"
                      x-ui-placeholder: "Enter CPU limits for your application"
                      x-ui-error-message: "Value doesn't match pattern, it should
                        be number ranging from 1 to 32 or 1m to 32000m"
                    memory_limit:
                      type: string
                      title: Memory Limit
                      description: Memory limit size
                      pattern: "^([1-9]|[1-5][0-9]|6[0-4])Gi$|^([1-9]|[1-9][0-9]{1,3}|[1-5][0-9]{4}|6[0-3][0-9]{3}|64000)Mi$"
                      x-ui-placeholder: "Enter Memory limits for your application"
                      x-ui-error-message: "Value doesn't match pattern, it should
                        be number ranging from 1Gi to 64Gi or 1Mi to 64000Mi"
                  required:
                  - cpu
                  - memory
                volumes:
                  type: object
                  title: Volumes
                  x-ui-override-disable: true
                  x-ui-toggle: true
                  description: Manage data storage volumes to ensure access and persistence.
                  properties:
                    config_maps:
                      type: object
                      title: Config Maps
                      description: Config map mounts
                      patternProperties:
                        "^[a-zA-Z0-9_.-]*$":
                          type: object
                          title: Config Map Name
                          properties:
                            name:
                              type: string
                              title: Name
                              description: Config map name
                              pattern: "^[a-zA-Z0-9]([a-zA-Z0-9-]{1,18}[a-zA-Z0-9])$"
                              x-ui-error-message: "Value doesn't match pattern, it
                                should start with an alphanumeric character and should
                                be in range of 3-20 characters"
                              x-ui-typeable: true
                              x-ui-api-source:
                                endpoint: "/cc-ui/v1/dropdown/stack/{{stackName}}/resources-info"
                                method: GET
                                params:
                                  includeContent: false
                                labelKey: resourceName
                                valueKey: resourceName
                                valueTemplate: "${config_map.{{value}}.out.attributes.name}"
                                filterConditions:
                                - field: resourceType
                                  value: config_map
                            mount_path:
                              type: string
                              title: Mount Path
                              description: Mount path for config maps
                            sub_path:
                              type: string
                              title: Sub Path
                              description: Path within the ConfigMap to mount a specific
                                key or file
                          required:
                          - name
                          - mount_path
                    secrets:
                      type: object
                      title: Secrets
                      description: Secret mounts
                      patternProperties:
                        "^[a-zA-Z0-9_.-]*$":
                          type: object
                          title: Secret name
                          properties:
                            name:
                              type: string
                              title: Name
                              description: Secret name
                              x-ui-typeable: true
                              x-ui-api-source:
                                endpoint: "/cc-ui/v1/dropdown/stack/{{stackName}}/resources-info"
                                method: GET
                                params:
                                  includeContent: false
                                labelKey: resourceName
                                valueKey: resourceName
                                valueTemplate: "${kubernetes_secret.{{value}}.out.attributes.name}"
                                filterConditions:
                                - field: resourceType
                                  value: kubernetes_secret
                            mount_path:
                              type: string
                              title: Mount Path
                              description: Mount path for secrets
                            sub_path:
                              type: string
                              title: Sub Path
                              description: Path within the Secret to mount a specific
                                key or file
                          required:
                          - name
                          - mount_path
                    pvc:
                      type: object
                      title: PVC
                      description: PVC mounts
                      patternProperties:
                        "^[a-zA-Z0-9_.-]*$":
                          type: object
                          title: PVC Name
                          properties:
                            claim_name:
                              type: string
                              title: Claim Name
                              description: PVC name
                              pattern: "^[a-zA-Z0-9]([a-zA-Z0-9-]{1,18}[a-zA-Z0-9])$"
                              x-ui-error-message: "Value doesn't match pattern, it
                                should start with an alphanumeric character and should
                                be in range of 3-20 characters"
                              x-ui-typeable: true
                              x-ui-api-source:
                                endpoint: "/cc-ui/v1/dropdown/stack/{{stackName}}/resources-info"
                                method: GET
                                params:
                                  includeContent: false
                                labelKey: resourceName
                                valueKey: resourceName
                                valueTemplate: "${pvc.{{value}}.out.attributes.name}"
                                filterConditions:
                                - field: resourceType
                                  value: pvc
                            mount_path:
                              type: string
                              title: Mount Path
                              description: Mount path for PVC
                            sub_path:
                              type: string
                              title: Sub Path
                              description: Path within the PVC to mount a specific
                                subdirectory
                          required:
                          - claim_name
                          - mount_path
                    host_path:
                      type: object
                      title: Host Path
                      description: Host path mounts
                      patternProperties:
                        "^[a-zA-Z0-9_.-]*$":
                          type: object
                          title: Host Path Name
                          properties:
                            mount_path:
                              type: string
                              title: Mount Path
                              description: Mount path for host path
                            sub_path:
                              type: string
                              title: Sub Path
                              description: Path within the host filesystem to mount
                                a specific directory or file
                          required:
                          - name
                          - mount_path
              required:
              - size
              x-ui-order:
              - size
              - volumes
              - command
              - args
          required:
          - image
          - pull_policy
          - runtime
    sidecars:
      title: Sidecar Containers
      description: Map of specialized additional containers that run alongside a primary
        application container within a Pod.
      type: object
      x-ui-toggle: true
      patternProperties:
        "^[a-z]([-a-z0-9]{0,61}[a-z0-9])?$":
          title: Sidecar Container
          description: Sidecar Container Specification
          type: object
          properties:
            image:
              type: string
              title: Image
              description: Docker image of the init container
              x-ui-error-message: "Value doesn't match pattern for a container image"
            pull_policy:
              title: Image Pull Policy
              description: Specifies when the image should be pulled from the registry.
              type: string
              default: IfNotPresent
              enum:
              - IfNotPresent
              - Always
              - Never
            env:
              title: Environment Variables
              description: Map of environment variables passed to init container
              type: object
              patternProperties:
                "^[a-zA-Z][a-zA-Z0-9_-.]*$":
                  title: Evironment Variable
                  description: Environment Variable
                  type: string
              x-ui-yaml-editor: true
            additional_k8s_env:
              title: Additional Environment Variables
              description: List of evironment variables to be created from referencing
                key from configmap or secret. Eg 
                https://raw.githubusercontent.com/kubernetes/website/main/content/en/examples/pods/pod-configmap-env-var-valueFrom.yaml
              type: object
              x-ui-yaml-editor: true
            additional_k8s_env_from:
              title: Import Additional Environment Variables
              description: List of configmap or secret reference from which environments
                varibales to be imported. Eg. 
                https://raw.githubusercontent.com/kubernetes/website/main/content/en/examples/pods/inject/pod-secret-envFrom.yaml
              type: object
              x-ui-yaml-editor: true
            runtime:
              type: object
              title: Runtime
              description: Customize runtime settings like size, healthchecks etc.
              properties:
                command:
                  type: array
                  title: Command
                  description: Command to run in the container
                  items:
                    type: string
                  x-ui-command: true
                  x-ui-override-disable: true
                args:
                  type: array
                  title: Arguments
                  description: Arguments to pass to the command
                  items:
                    type: string
                  x-ui-command: true
                  x-ui-override-disable: true
                size:
                  type: object
                  title: Size
                  description: Configure container size to meet resource requirements
                  properties:
                    cpu:
                      type: string
                      title: CPU
                      description: CPU size
                      pattern: "^([1-9]|[12][0-9]|3[0-2])$|^(3[0-1][0-9]{3}|[1-2][0-9]{4}|[1-9][0-9]{0,3}|32000)m$"
                      x-ui-placeholder: "Enter CPU requests for your application"
                      x-ui-error-message: "Value doesn't match pattern, it should
                        be number ranging from 1 to 32 or 1m to 32000m"
                    memory:
                      type: string
                      title: Memory
                      description: Memory size
                      pattern: "^([1-9]|[1-5][0-9]|6[0-4])Gi$|^([1-9]|[1-9][0-9]{1,3}|[1-5][0-9]{4}|6[0-3][0-9]{3}|64000)Mi$"
                      x-ui-placeholder: "Enter Memory requests for your application"
                      x-ui-error-message: "Value doesn't match pattern, it should
                        be number ranging from 1Gi to 64Gi or 1Mi to 64000Mi"
                    cpu_limit:
                      type: string
                      title: CPU Limit
                      description: CPU limit size
                      pattern: "^([1-9]|[12][0-9]|3[0-2])$|^(3[0-1][0-9]{3}|[1-2][0-9]{4}|[1-9][0-9]{0,3}|32000)m$"
                      x-ui-placeholder: "Enter CPU limits for your application"
                      x-ui-error-message: "Value doesn't match pattern, it should
                        be number ranging from 1 to 32 or 1m to 32000m"
                    memory_limit:
                      type: string
                      title: Memory Limit
                      description: Memory limit size
                      pattern: "^([1-9]|[1-5][0-9]|6[0-4])Gi$|^([1-9]|[1-9][0-9]{1,3}|[1-5][0-9]{4}|6[0-3][0-9]{3}|64000)Mi$"
                      x-ui-placeholder: "Enter Memory limits for your application"
                      x-ui-error-message: "Value doesn't match pattern, it should
                        be number ranging from 1Gi to 64Gi or 1Mi to 64000Mi"
                  required:
                  - cpu
                  - memory
                volumes:
                  type: object
                  title: Volumes
                  x-ui-override-disable: true
                  x-ui-toggle: true
                  description: Manage data storage volumes to ensure access and persistence
                  properties:
                    config_maps:
                      type: object
                      title: Config Maps
                      description: Config map mounts
                      patternProperties:
                        "^[a-zA-Z0-9_.-]*$":
                          type: object
                          title: Config Map Name
                          properties:
                            name:
                              type: string
                              title: Name
                              description: Config map name
                              pattern: "^[a-zA-Z0-9]([a-zA-Z0-9-]{1,18}[a-zA-Z0-9])$"
                              x-ui-error-message: "Value doesn't match pattern, it
                                should start with an alphanumeric character and should
                                be in range of 3-20 characters"
                              x-ui-typeable: true
                              x-ui-api-source:
                                endpoint: "/cc-ui/v1/dropdown/stack/{{stackName}}/resources-info"
                                method: GET
                                params:
                                  includeContent: false
                                labelKey: resourceName
                                valueKey: resourceName
                                valueTemplate: "${config_map.{{value}}.out.attributes.name}"
                                filterConditions:
                                - field: resourceType
                                  value: config_map
                            mount_path:
                              type: string
                              title: Mount Path
                              description: Mount path for config maps
                            sub_path:
                              type: string
                              title: Sub Path
                              description: Path within the ConfigMap to mount a specific
                                key or file
                          required:
                          - name
                          - mount_path
                    secrets:
                      type: object
                      title: Secrets
                      description: Secret mounts
                      patternProperties:
                        "^[a-zA-Z0-9_.-]*$":
                          type: object
                          title: Secret name
                          properties:
                            name:
                              type: string
                              title: Name
                              description: Secret name
                              x-ui-typeable: true
                              x-ui-api-source:
                                endpoint: "/cc-ui/v1/dropdown/stack/{{stackName}}/resources-info"
                                method: GET
                                params:
                                  includeContent: false
                                labelKey: resourceName
                                valueKey: resourceName
                                valueTemplate: "${kubernetes_secret.{{value}}.out.attributes.name}"
                                filterConditions:
                                - field: resourceType
                                  value: kubernetes_secret
                            mount_path:
                              type: string
                              title: Mount Path
                              description: Mount path for secrets
                            sub_path:
                              type: string
                              title: Sub Path
                              description: Path within the Secret to mount a specific
                                key or file
                          required:
                          - name
                          - mount_path
                    pvc:
                      type: object
                      title: PVC
                      description: PVC mounts
                      patternProperties:
                        "^[a-zA-Z0-9_.-]*$":
                          type: object
                          title: PVC Name
                          properties:
                            claim_name:
                              type: string
                              title: Claim Name
                              description: PVC name
                              pattern: "^[a-zA-Z0-9]([a-zA-Z0-9-]{1,18}[a-zA-Z0-9])$"
                              x-ui-error-message: "Value doesn't match pattern, it
                                should start with an alphanumeric character and should
                                be in range of 3-20 characters"
                              x-ui-typeable: true
                              x-ui-api-source:
                                endpoint: "/cc-ui/v1/dropdown/stack/{{stackName}}/resources-info"
                                method: GET
                                params:
                                  includeContent: false
                                labelKey: resourceName
                                valueKey: resourceName
                                valueTemplate: "${pvc.{{value}}.out.attributes.name}"
                                filterConditions:
                                - field: resourceType
                                  value: pvc
                            mount_path:
                              type: string
                              title: Mount Path
                              description: Mount path for PVC
                            sub_path:
                              type: string
                              title: Sub Path
                              description: Path within the PVC to mount a specific
                                subdirectory
                          required:
                          - claim_name
                          - mount_path
                    host_path:
                      type: object
                      title: Host Path
                      description: Host path mounts
                      patternProperties:
                        "^[a-zA-Z0-9_.-]*$":
                          type: object
                          title: Host Path Name
                          properties:
                            mount_path:
                              type: string
                              title: Mount Path
                              description: Mount path for host path
                            sub_path:
                              type: string
                              title: Sub Path
                              description: Path within the host filesystem to mount
                                a specific directory or file
                          required:
                          - name
                          - mount_path
                ports:
                  type: object
                  title: Ports
                  x-ui-override-disable: true
                  description: Port mappings for sidecar container
                  patternProperties:
                    "^[0-9]+[m]?$":
                      type: object
                      title: Port Name
                      description: Define port to be exposed from sidecar
                      properties:
                        port:
                          type: string
                          title: Port
                          description: Port on which sidecar application is running
                          pattern: "^(?!0$)([1-9][0-9]{0,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])$"
                          x-ui-placeholder: "Enter the port number to expose for your
                            sidecar container"
                          x-ui-error-message: "Value doesn't match pattern, it should
                            be number ranging from 1-65535"
                      required:
                      - port
                health_checks:
                  type: object
                  title: Health Checks
                  description: Ensure service remains responsive and operational.
                    Assess its status and performance.
                  x-ui-override-disable: true
                  x-ui-toggle: true
                  properties:
                    readiness_check_type:
                      type: string
                      title: Readiness Check Type
                      default: None
                      x-ui-no-sort: true
                      enum:
                      - None
                      - PortCheck
                      - HttpCheck
                      - ExecCheck
                    readiness_start_up_time:
                      type: integer
                      title: Readiness Start-Up Time
                      default: 10
                      minimum: 0
                      maximum: 10000
                      x-ui-placeholder: "Enter readiness start up time for the Pod"
                      x-ui-error-message: "Value doesn't match pattern, accepted values
                        are from 0-10000"
                      x-ui-visible-if:
                        field: spec.sidecars.{{this}}.runtime.health_checks.readiness_check_type
                        values: ["PortCheck", "HttpCheck", "ExecCheck"]
                      description: Wait time for readiness check (in sec)
                    readiness_timeout:
                      type: integer
                      title: Readiness Timeout
                      default: 10
                      minimum: 0
                      maximum: 10000
                      x-ui-placeholder: "Enter readiness timeout for the Pod"
                      x-ui-error-message: "Value doesn't match pattern, accepted values
                        are from 0-10000"
                      x-ui-visible-if:
                        field: spec.sidecars.{{this}}.runtime.health_checks.readiness_check_type
                        values: ["PortCheck", "HttpCheck", "ExecCheck"]
                      description: Max time for readiness (in sec)
                    readiness_period:
                      type: integer
                      title: Readiness Check Interval
                      default: 10
                      minimum: 0
                      maximum: 10000
                      x-ui-placeholder: "Enter readiness period for the Pod"
                      x-ui-error-message: "Value doesn't match pattern, accepted values
                        are from 0-10000"
                      x-ui-visible-if:
                        field: spec.sidecars.{{this}}.runtime.health_checks.readiness_check_type
                        values: ["PortCheck", "HttpCheck", "ExecCheck"]
                      description: Repeated interval for health-check (in sec)
                    readiness_url:
                      type: string
                      title: Readiness URL
                      pattern: "^(/[^/]+)*(/)?$"
                      x-ui-placeholder: "Enter readiness url for the Pod"
                      x-ui-error-message: "Value doesn't match pattern, eg: / , /<path>"
                      x-ui-visible-if:
                        field: spec.sidecars.{{this}}.runtime.health_checks.readiness_check_type
                        values: ["HttpCheck"]
                      description: URL for readiness
                    readiness_exec_command:
                      type: array
                      title: Readiness Exec Command
                      pattern: '^([^,]+)(,\s*[^,]+)*$'
                      x-ui-placeholder: "Enter readiness exec for the Pod, commands
                        are separated by commas"
                      x-ui-error-message: "Value doesn't match pattern, commands needs
                        to be comma separated"
                      x-ui-visible-if:
                        field: spec.sidecars.{{this}}.runtime.health_checks.readiness_check_type
                        values: ["ExecCheck"]
                      description: Commands for readiness check
                      items:
                        type: string
                      x-ui-override-disable: true
                    liveness_check_type:
                      type: string
                      title: Liveness Check Type
                      default: None
                      x-ui-no-sort: true
                      enum:
                      - None
                      - PortCheck
                      - HttpCheck
                      - ExecCheck
                    liveness_start_up_time:
                      type: integer
                      default: 10
                      title: Liveness Start-Up Time
                      minimum: 0
                      maximum: 10000
                      x-ui-placeholder: "Enter liveness start up time for the Pod"
                      x-ui-error-message: "Value doesn't match pattern, accepted values
                        are from 0-10000"
                      x-ui-visible-if:
                        field: spec.sidecars.{{this}}.runtime.health_checks.liveness_check_type
                        values: ["PortCheck", "HttpCheck", "ExecCheck"]
                      description: Wait time for liveness (in sec)
                    liveness_timeout:
                      type: integer
                      default: 10
                      title: Liveness Timeout
                      minimum: 0
                      maximum: 10000
                      x-ui-placeholder: "Enter liveness timeout for the Pod"
                      x-ui-error-message: "Value doesn't match pattern, accepted values
                        are from 0-10000"
                      x-ui-visible-if:
                        field: "spec.sidecars.{{this}}.runtime.health_checks.liveness_check_type"
                        values: ["PortCheck", "HttpCheck", "ExecCheck"]
                      description: Max time for liveness (in sec)
                    liveness_period:
                      type: integer
                      title: Liveness Check Interval
                      default: 10
                      minimum: 0
                      maximum: 10000
                      x-ui-placeholder: "Enter liveness period for the Pod"
                      x-ui-error-message: "Value doesn't match pattern, accepted values
                        are from 0-10000"
                      x-ui-visible-if:
                        field: "spec.sidecars.{{this}}.runtime.health_checks.liveness_check_type"
                        values: ["PortCheck", "HttpCheck", "ExecCheck"]
                      description: Interval for Liveness (in sec)
                    liveness_url:
                      type: string
                      title: Liveness URL
                      pattern: "^(/[^/]+)*(/)?$"
                      x-ui-placeholder: "Enter liveness url for the Pod"
                      x-ui-error-message: "Value doesn't match pattern, eg: / , /<path>"
                      x-ui-visible-if:
                        field: "spec.sidecars.{{this}}.runtime.health_checks.liveness_check_type"
                        values: ["HttpCheck"]
                      description: URL for liveness
                    liveness_exec_command:
                      type: array
                      title: Liveness Exec Command
                      pattern: '^([^,]+)(,\s*[^,]+)*$'
                      x-ui-placeholder: "Enter readiness exec for the Pod, commands
                        are separated by commas"
                      x-ui-error-message: "Value doesn't match pattern, commands needs
                        to be comma separated"
                      x-ui-visible-if:
                        field: "spec.sidecars.{{this}}.runtime.health_checks.liveness_check_type"
                        values: ["ExecCheck"]
                      description: Commands for liveness
                      items:
                        type: string
                      x-ui-override-disable: true
                    port:
                      type: string
                      title: Health Check Port
                      x-ui-placeholder: "Enter health check port for the Sidecar"
                      x-ui-error-message: "Value doesn't match pattern, accepted values
                        are from 0-65535"
                      x-ui-visible-if:
                        field: "spec.sidecars.{{this}}.runtime.health_checks.readiness_check_type"
                        values: ["PortCheck", "HttpCheck"]
                      description: Port for Health Checks
                      x-ui-dynamic-enum: spec.sidecars.{{this}}.runtime.ports.*.port
                      x-ui-disable-tooltip: "No Ports Added"
                      x-ui-typeable: true
                  required:
                  - readiness_url
                  - readiness_exec_command
                  - liveness_url
                  - liveness_exec_command
                  - port
              required:
              - size
              x-ui-order:
              - size
              - volumes
              - command
              - args
              - ports
              - health_checks
          required:
          - image
          - pull_policy
          - runtime
  required:
  - persistent_volume_claims
  x-ui-order:
  - restart_policy
  - enable_host_anti_affinity
  - persistent_volume_claims
  - runtime
  - release
  - init_containers
  - sidecars
  - cloud_permissions
sample:
  $schema: >-
    https://facets-cloud.github.io/facets-schemas/schemas/service/service.schema.json
  flavor: statefulset
  metadata:
    labels:
      deliveryType: MANUAL
  kind: service
  disabled: true
  version: "0.1"
  spec:
    enable_host_anti_affinity: false
    type: statefulset
    persistent_volume_claims: {}
    release:
      strategy:
        type: RollingUpdate
    runtime:
      args: []
      command: []
      autoscaling:
        scaling_on: CPU
        min: "1"
        max: "1"
        cpu_threshold: "50"
      ports: {}
      size:
        cpu: 300m
        memory: 1Gi
        cpu_limit: 1000m
        memory_limit: 5Gi
      volumes: {}
    env:
      LOG_LEVEL: INFO
    init_containers: {}
    sidecars: {}
