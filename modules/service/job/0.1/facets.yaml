intent: service
flavor: job
version: '0.1'
description: Adds service module
clouds:
- aws
- azure
- gcp
- kubernetes
artifact_inputs:
  primary:
    attribute_path: "spec.release.image"
    artifact_type: "docker_image"
metadata:
  title: Metadata of service
  type: object
  properties:
    namespace:
      type: string
      title: Namespace
      description: Namespace in which job should be deployed
      default: default
spec:
  title: K8s flavor of service
  type: object
  properties:
    type:
      type: string
      title: Service Type
      description: Type - Job
      x-ui-override-disable: true
      default: job
    job:
      type: object
      title: Job
      description: Manage execution of a one-off task or batch process, ensuring it
        completes successfully
      x-ui-override-disable: true
      x-ui-toggle: true
      properties:
        retry:
          type: string
          default: 5
          minimum: 1
          x-ui-placeholder: "Enter the minimum retry count for your job (default is
            5)"
          description: Number of time you want to retry the job before calling it
            a failure
          pattern: '^(100|[1-9][0-9]?)$'
          x-ui-error-message: "The retry count must be a positive integer between
            1 and 100."
      required:
      - retry
    runtime:
      type: object
      title: Runtime
      description: Customize service runtime settings like argument, command.
      properties:
        command:
          type: array
          title: Command
          description: Command to run in the container
          items:
            type: string
          x-ui-command: true
          x-ui-override-disable: true
        args:
          type: array
          title: Arguments
          description: Arguments to pass to the command
          pattern: '^[a-zA-Z0-9-_ ]+$'
          x-ui-error-message: "Each argument can only contain alphanumeric characters,
            dashes, underscores, and spaces."
          items:
            type: string
          x-ui-command: true
          x-ui-override-disable: true
      x-ui-order:
      - args
      - command
  required:
  - job
  x-ui-order:
  - job
  - runtime
sample:
  $schema: >-
    https://facets-cloud.github.io/facets-schemas/schemas/service/service.schema.json
  metadata:
    name: job-service
  disabled: true
  flavor: job
  kind: service
  version: '0.1'
  spec:
    type: job
    job: {}
