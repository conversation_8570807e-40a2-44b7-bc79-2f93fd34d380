intent: ingress
flavor: aws_alb
version: '0.2'
description: Adds ingress - aws_alb flavor
clouds:
- aws
sample:
  kind: ingress
  $schema: 
    https://facets-cloud.github.io/facets-schemas/schemas/ingress/ingress.schema.json
  flavor: aws_alb
  disabled: true
  version: '0.2'
  metadata:
    annotations: {}
  spec:
    private: false
    basic_auth: false
    grpc: false
    domains: {}
    rules:
      grafana1:
        service_name: prometheus-operator-grafana
        path: /
        port: 80
        domain_prefix: grafana1
        annotations:
          app: ''
        disable_auth: false
    force_ssl_redirection: true
spec:
  title: AWS ALB Ingress Controller
  type: object
  properties:
    basic_auth:
      type: boolean
      title: Basic Authentication
      description: Enable/disable basic auth
    grpc:
      type: boolean
      title: GRPC
      description: Enable/disable GRPC support
    private:
      type: boolean
      title: Private
      description: Set load balancer as private
    domains:
      title: Domains
      description: Map of domain key to rules
      type: object
      x-ui-overrides-only: true
      patternProperties:
        "^[a-zA-Z0-9_.-]*$":
          title: Domain Object
          description: Name of the domain object
          properties:
            domain:
              type: string
              title: Domain
              description: Host name of the domain
              pattern: '^(?=.{1,253}$)(?!-)[A-Za-z0-9-]{1,63}(?<!-)(\.[A-Za-z0-9-]{1,63})*\.[A-Za-z]{2,6}$'
              x-ui-placeholder: "Domain to map ingress. Eg: example.com, sub.example.com,
                my-domain.co.uk"
              x-ui-error-message: "Value doesn't match the format. Eg: example.com,
                my-domain.co.uk"
            alias:
              type: string
              title: Alias
              description: Alias for the domain
          required: ["domain", "alias"]
          type: object
    rules:
      title: Ingress Rules
      description: Objects of all ingress rules
      type: object
      patternProperties:
        "^[a-zA-Z0-9_.-]*$":
          title: Ingress Object
          description: Name of the ingress object
          properties:
            service_name:
              type: string
              title: Service Name
              description: Kubernetes service name
              x-ui-api-source:
                endpoint: "/cc-ui/v1/dropdown/stack/{{stackName}}/resources-info"
                method: GET
                params:
                  includeContent: false
                labelKey: resourceName
                valueKey: resourceName
                valueTemplate: "${service.{{value}}.out.attributes.service_name}"
                filterConditions:
                - field: resourceType
                  value: service
              x-ui-typeable: true
            path:
              type: string
              title: Path
              description: Path of the application
              pattern: '^(/[^/]+)*(/)?$'
              x-ui-placeholder: "Enter path to which your application will be accessible"
              x-ui-error-message: "Value doesn't match pattern, eg: / or /<path> etc"
              default: '/'
            port:
              type: string
              title: Port
              description: Service port number
              x-ui-api-source:
                endpoint: "/cc-ui/v1/dropdown/stack/{{stackName}}/service/{{serviceName}}/overview"
                dynamicProperties:
                  serviceName:
                    key: service_name
                    lookup: regex
                    x-ui-lookup-regex: '\${[^.]+\.([^.]+).*'
                method: GET
                labelKey: port
                valueKey: port
                path: ports
              x-ui-disable-tooltip: "No Ports Found or Service not selected"
              x-ui-typeable: true
            domain_prefix:
              type: string
              title: Domain Prefix
              description: Subdomain prefix
              pattern: '^(?!-)[a-z0-9-]{1,18}(?<!-)$'
              x-ui-placeholder: "Enter the subdomain you want to expose your application"
              x-ui-error-message: "Value doesn't match pattern, max characters of
                20, only hyphen special characters is accepted with numbers and alphabets
                and should not start or end with hyphen"
            grpc:
              type: boolean
              title: GRPC
              description: Enable/disable GRPC protocol
              x-ui-visible-if:
                field: spec.grpc
                values: ["false"]
          required: ["service_name", "path", "port"]
          type: object
    force_ssl_redirection:
      type: boolean
      title: Force SSL Redirection
      description: Force HTTP to HTTPS redirection
  required: ["private", "rules", "force_ssl_redirection"]
  x-ui-order: ["basic_auth", "grpc", "private", "domains", "rules", "force_ssl_redirection"]
