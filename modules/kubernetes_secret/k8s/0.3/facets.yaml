intent: kubernetes_secret
flavor: k8s
version: '0.3'
description: "kubernetes_secret - k8s"
clouds:
- aws
- azure
- gcp
- kubernetes
spec:
  type: object
  properties:
    data:
      title: Data
      type: object
      description: Data objects containing key, value pair
      patternProperties:
        "^[a-zA-Z0-9_.-]*$":
          title: Data Object Block
          description: something
          type: object
          properties:
            key:
              title: Kubernetes secret Data Key name
              description: Key name of the kubernetes secret object
              type: string
            value:
              title: Kubernetes secret Data Object Value
              description: Value of the kubernetes secret object
              type: string
              x-ui-textarea: true
sample:
  $schema: >-
    https://facets-cloud.github.io/facets-schemas/schemas/kubernetes_secret/kubernetes_secret.schema.json
  kind: kubernetes_secret
  disabled: true
  flavor: k8s
  version: '0.3'
  metadata: {}
  spec:
    data: {}
