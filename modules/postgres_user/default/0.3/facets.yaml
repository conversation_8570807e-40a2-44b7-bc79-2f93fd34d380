intent: postgres_user
flavor: default
version: "0.3"
description: Adds postgres_user - default flavor
clouds:
- aws
- gcp
- azure
- kubernetes
lifecycle: ENVIRONMENT
input_type: instance
composition: {}
inputs:
  kubernetes_details:
    type: "@output/kubernetes"
    displayName: Kubernetes Cluster
    description: Details of Kubernetes where the CRD for managing postgresql user
      will be created
    optional: false
    default:
      resource_type: kubernetes_cluster
      resource_name: default
  database_operator_details:
    type: "@output/database_operator"
    displayName: Database Operator
    description: Details of database operator which will be responsible to create
      postgres users
    optional: false
  postgres_details:
    type: "@output/postgres"
    displayName: Postgres
    description: Details of Postgres where the user needs to be created
    optional: false
spec:
  title: Postgres User Spec
  description: Specification for postgres user
  type: object
  properties:
    postgres_user:
      title: Postgres User
      description: Specification for postgres user and role
      type: object
      properties:
        role_name:
          title: Role Name
          description: Name of role and user to be created in postgres
          type: string
          pattern: "^[a-zA-Z]([a-zA-Z0-9-]*[a-zA-Z0-9])?$"
        role:
          title: Role
          description: Postgres role Specification
          type: object
          properties:
            connection_limit:
              title: Connection Limit
              description: Maximum number of connections that can be made to the database
                using this role
              type: integer
              minimum: 1
            privileges:
              title: Privileges
              description: List of privileges that will be granted to the role
              type: object
              properties:
                bypass_rls:
                  title: Bypass RLS
                  description: Bypass Row Level Security
                  type: boolean
                  default: false
                create_db:
                  title: Create DB
                  description: Create Database
                  type: boolean
                  default: false
                create_role:
                  title: Create Role
                  description: Create Role
                  type: boolean
                  default: false
                inherit:
                  title: Inherit
                  description: Inherit privileges from other roles
                  type: boolean
                  default: false
                login:
                  title: Login
                  description: Login to the database
                  type: boolean
                  default: false
                replication:
                  title: Replication
                  description: Replication
                  type: boolean
                  default: false
                superuser:
                  title: Superuser
                  description: Superuser
                  type: boolean
                  default: false
        user_password:
          title: User Password
          description: Password for the user
          type: string
        grant_statements:
          title: Grant Statements
          description: Map of PostgreSQL database to grant queries that will be executed
            to grant permissions after role creation on particular database
          type: object
          minProperties: 1
          patternProperties:
            "^[a-zA-Z0-9_-]+$":
              type: object
              title: Grant Statements Key
              description: Grant Statement Object
              minProperties: 1
              properties:
                database:
                  type: string
                  title: Database
                  description: Name of the database to grant permissions
                  pattern: "^[a-zA-Z0-9_-]+$"
                  x-ui-unique: true
                statements:
                  type: array
                  title: Statements
                  description: List of grant statements to be executed
                  items:
                    type: string
                  minItems: 1
                  x-ui-override-disable: true
        connection_details:
          title: Connection Details
          description: Connection details for the postgres master user
          type: object
          properties:
            default_database:
              title: Default Database
              description: Default database to connect to
              type: string
              default: postgres
            sslmode:
              title: SSL Mode
              description: SSL mode to connect to the database
              type: string
              default: disable
      required:
      - grant_statements
sample:
  kind: postgres_user
  flavor: default
  version: "0.3"
  metadata: {}
  spec:
    postgres_user:
      role_name: test-role
      role:
        connection_limit: 100
        privileges:
          bypass_rls: false
          create_db: false
          create_role: false
          inherit: false
          login: false
          replication: false
          superuser: false
      user_password: password
      grant_statements:
        grant_statement_1:
          database: test_db
          statements:
          - 'GRANT CONNECT ON DATABASE postgres TO "test-role";'
          - 'GRANT USAGE ON SCHEMA public TO "test-role";'
          - 'GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO "test-role";'
          - 'ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO "test-role";'
          - 'GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO "test-role";'
          - 'GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO "test-role";'
          - 'ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT USAGE ON SEQUENCES TO
            "test-role";'
        grant_statement_2:
          database: test_db_2
          statements:
          - 'GRANT CONNECT ON DATABASE postgres TO "test-role";'
          - 'GRANT USAGE ON SCHEMA public TO "test-role";'
          - 'GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO "test-role";'
          - 'ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO "test-role";'
          - 'GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO "test-role";'
          - 'GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO "test-role";'
          - 'ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT USAGE ON SEQUENCES TO
            "test-role";'
      connection_details:
        default_database: postgres
        sslmode: disable
  disabled: true
