intent: kafka_topic
flavor: default
version: '0.1'
description: Adds kafka_topic - default flavor
clouds:
- aws
- gcp
- azure
- kubernetes
spec:
  title: Kafka Topic
  type: object
  description: Specifications of Kafka Topic for Kafka
  properties:
    topics:
      type: object
      title: Topics
      description: Contains a map of configurations for multiple Kafka Topics
      minProperties: 1
      patternProperties:
        '^[a-zA-Z0-9_.-]*$':
          title: Topics
          description: The name of the Kafka Topic to be created
          type: object
          properties:
            topic_name:
              title: Topic Name
              description: The name of the topic.
              type: string
              pattern: '^[a-zA-Z0-9_.-]*$'
            replication_factor:
              title: Replication Factor
              description: The replication factor for each partition in the topic
                being created.
              type: integer
            partitions:
              title: Partitions
              description: "The number of partitions for the topic being created or
                altered."
              type: integer
            configs:
              title: Configs
              description: A topic configuration override for the topic
              type: object
              x-ui-yaml-editor: true
              x-ui-placeholder: "Enter the configs key/value pair. Eg. segment.ms:
                20000"
          required:
          - topic_name
          - replication_factor
          - partitions
inputs:
  crossplane_details:
    type: "@output/crossplane"
    displayName: Crossplane
    description: Details of crossplane which will be responsible to create mysql users
    optional: false
  kafka_details:
    type: "@outputs/kafka"
    optional: false
    displayName: Kafka
    description: Kafka instance to create the topic in
  kubernetes_details:
    displayName: Kubernetes Cluster
    description: Kubernetes Cluster where crossplane is installed
    optional: false
    type: "@outputs/kubernetes"
    default:
      resource_type: kubernetes_cluster
      resource_name: default
    providers:
      - kubernetes
      - kubernetes-alpha
      - helm
sample:
  $schema: 
    https://facets-cloud.github.io/facets-schemas/schemas/kafka_topic/kafka_topic.schema.json
  kind: kafka_topic
  flavor: default
  version: '0.1'
  disabled: false
  metadata: {}
  out: {}
  spec: {}
