intent: redis
flavor: elasticache
version: '0.2'
description: Adds redis module of flavor elasticache
clouds:
  - aws
spec:
  title: Redis ElastiCache
  type: object
  properties:
    authenticated:
      type: boolean
      title: Authenticated
      description: Enables password protection.
    persistence_enabled:
      type: boolean
      title: Persistence Enabled
      description: Ensures data is preserved across restarts.
    redis_version:
      type: string
      title: Redis Version
      description: Specifies the Redis version to use.
      enum:
        - '5.0.6'
        - '6.x'
      x-ui-error-message: "Please select a supported Redis version."
    size:
      type: object
      title: Size
      description: Configuration for datastore components.
      properties:
        instance:
          type: string
          title: Instance Type
          description: The instance type of the node.
          x-ui-placeholder: e.g., 'cache.t3.micro', 'cache.m5.large', 'cache.r5.xlarge'
          enum:
            - cache.t4g.micro
            - cache.t4g.small
            - cache.t4g.medium
            - cache.t3.micro
            - cache.t3.small
            - cache.t3.medium
            - cache.m5.large
            - cache.m5.xlarge
            - cache.m5.2xlarge
            - cache.m5.4xlarge
            - cache.m5.12xlarge
            - cache.m5.24xlarge
            - cache.r6g.large
            - cache.r6g.xlarge
            - cache.r6g.2xlarge
            - cache.r6g.4xlarge
            - cache.r6g.8xlarge
            - cache.r6g.12xlarge
            - cache.r6g.16xlarge
            - cache.r7g.large
            - cache.r7g.xlarge
            - cache.r7g.2xlarge
            - cache.r7g.4xlarge
            - cache.r7g.8xlarge
            - cache.r7g.12xlarge
            - cache.r7g.16xlarge
            - cache.c7gn.large
            - cache.c7gn.xlarge
            - cache.c7gn.2xlarge
            - cache.c7gn.4xlarge
            - cache.c7gn.8xlarge
            - cache.c7gn.12xlarge
            - cache.c7gn.16xlarge
          x-ui-error-message: "Please select a valid instance type."
        instance_count:
          type: integer
          title: Instance Count
          description: Number of instances to create.
          default: 1
          minimum: 1
          maximum: 10
          x-ui-error-message: "Instance count must be between 1 and 10."
          required:
            - instance
          x-ui-order:
            - instance
            - instance_count
  required:
    - size
    - authenticated
    - persistence_enabled
    - redis_version
  x-ui-order:
    - authenticated
    - persistence_enabled
    - redis_version
    - size
sample:
  $schema: 'https://facets-cloud.github.io/facets-schemas/schemas/redis/redis.schema.json'
  flavor: elasticache
  metadata:
    tags:
      managed-by: facets
  kind: redis
  disabled: true
  version: '0.2'
  spec:
    authenticated: false
    redis_version: '6.x'
    persistence_enabled: false
    size:
      instance_count: 1
      instance: cache.t3.micro
