name: google_cloud_storage_bucket
out:
  type: object
  title: GCS Bucket Output
  description: Output attributes for a Google Cloud Storage bucket

  properties:
    bucket_url:
      type: string
      description: Full URL of the GCS bucket in the format gs://bucket-name

    bucket_name:
      type: string
      description: The name of the created GCS bucket

    bucket_location:
      type: string
      description: The location where the bucket is stored

    bucket_self_link:
      type: string
      description: The self link of the created GCS bucket

    bucket_storage_class:
      type: string
      description: The storage class of the bucket
      enum:
        - STANDARD
        - NEARLINE
        - COLDLINE
        - ARCHIVE

    bucket_iam_condition_expression:
      type: string
      description: CEL expression for IAM condition to restrict access to this specific bucket
